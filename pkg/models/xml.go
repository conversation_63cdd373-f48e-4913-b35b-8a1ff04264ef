package models

import "encoding/xml"

// MessageReceive represents the outer layer of received request data, mainly used to determine data type
type MessageReceive struct {
	CmdType string `xml:"CmdType"`
	SN      int    `xml:"SN"`
}

// CatalogResponse represents catalog response from platform
type CatalogResponse struct {
	XMLName    xml.Name `xml:"Response"`
	CmdType    string   `xml:"CmdType"`
	SN         int      `xml:"SN"`
	DeviceID   string   `xml:"DeviceID"`
	SumNum     int      `xml:"SumNum"`
	DeviceList struct {
		Devices []Device `xml:"Item"`
	} `xml:"DeviceList"`
}

// DeviceInfoResponse represents device info response from platform
type DeviceInfoResponse struct {
	XMLName      xml.Name `xml:"Response"`
	CmdType      string   `xml:"CmdType"`
	SN           int      `xml:"SN"`
	DeviceID     string   `xml:"DeviceID"`
	DeviceName   string   `xml:"DeviceName"`
	Manufacturer string   `xml:"Manufacturer"`
	Model        string   `xml:"Model"`
	Firmware     string   `xml:"Firmware"`
	Channel      int      `xml:"Channel"`
}

// KeepaliveNotify represents keepalive notification from platform
type KeepaliveNotify struct {
	XMLName  xml.Name `xml:"Notify"`
	CmdType  string   `xml:"CmdType"`
	SN       int      `xml:"SN"`
	DeviceID string   `xml:"DeviceID"`
	Status   string   `xml:"Status"`
}

// CatalogQuery represents catalog query request to platform
type CatalogQuery struct {
	XMLName  xml.Name `xml:"Query"`
	CmdType  string   `xml:"CmdType"`
	SN       int      `xml:"SN"`
	DeviceID string   `xml:"DeviceID"`
}

// DeviceInfoQuery represents device info query request to platform
type DeviceInfoQuery struct {
	XMLName  xml.Name `xml:"Query"`
	CmdType  string   `xml:"CmdType"`
	SN       int      `xml:"SN"`
	DeviceID string   `xml:"DeviceID"`
}

// PTZControl represents PTZ control command
type PTZControl struct {
	XMLName    xml.Name `xml:"Control"`
	CmdType    string   `xml:"CmdType"`
	SN         int      `xml:"SN"`
	DeviceID   string   `xml:"DeviceID"`
	PTZCmd     string   `xml:"PTZCmd"`
	Info       PTZInfo  `xml:"Info"`
}

// PTZInfo represents PTZ control info
type PTZInfo struct {
	ControlPriority int `xml:"ControlPriority"`
}
